#!/usr/bin/env python3
"""
Test Data Validator Module

This module provides validation functions to ensure that test scripts only run
with meaningful, user-provided test data rather than placeholder values.

The validator checks for:
1. Existence of test_data_runtime.json file (indicates Stage 5 completion)
2. Placeholder patterns in manual input data
3. Empty or missing manual input values
4. Fallback data indicators

Usage:
    from test_data_validator import validate_test_data
    
    def test_my_function(browser, test_data):
        validate_test_data(test_data)  # Will skip test if data is invalid
        # ... rest of test logic
"""

import os
import pytest
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Define placeholder patterns that indicate invalid test data
PLACEHOLDER_PATTERNS = [
    "test_value_",
    "placeholder_", 
    "default_",
    "sample_",
    "example_",
    "dummy_",
    "mock_",
    "fake_",
    "any_user",
    "any_password",
    "test_user",
    "test_password"
]

# Define common placeholder values
PLACEHOLDER_VALUES = [
    "test_value_1",
    "test_value_2", 
    "test_value_3",
    "test_value_4",
    "test_value_5",
    "placeholder",
    "default",
    "sample",
    "example",
    "dummy",
    "mock",
    "fake"
]


def validate_test_data(test_data):
    """
    Validate that test data contains real user input, not placeholders.
    
    This function performs comprehensive validation of test data to ensure
    that tests only run with meaningful, user-provided values. If validation
    fails, the test is skipped with a descriptive message.
    
    Args:
        test_data (dict): Test data dictionary from the test_data fixture
        
    Raises:
        pytest.skip: If test data validation fails
        
    Returns:
        bool: True if validation passes
    """
    logger.info("Starting test data validation...")
    
    # Check 1: Verify test_data_runtime.json exists (indicates Stage 5 completion)
    runtime_file = "test_data_runtime.json"
    if not os.path.exists(runtime_file):
        error_msg = (
            "Manual input data not available - test_data_runtime.json file not found. "
            "Please complete Stage 5 data entry first to provide real test data."
        )
        logger.warning(f"Test data validation failed: {error_msg}")
        pytest.skip(error_msg)
    
    # Check 2: Verify test_data is not None or empty
    if not test_data or not isinstance(test_data, dict):
        error_msg = "Test data is empty or invalid. Please complete Stage 5 data entry first."
        logger.warning(f"Test data validation failed: {error_msg}")
        pytest.skip(error_msg)
    
    # Check 3: Look for fallback data indicators
    if test_data.get("_is_fallback_data"):
        error_msg = (
            "Test is using fallback data instead of real manual input. "
            "Please complete Stage 5 data entry to provide meaningful test data."
        )
        logger.warning(f"Test data validation failed: {error_msg}")
        pytest.skip(error_msg)
    
    # Check 4: Validate manual input fields
    manual_input_keys = [key for key in test_data.keys() if key.startswith('manual_input_for_step_')]
    
    if not manual_input_keys:
        error_msg = (
            "No manual input data found in test data. "
            "Please complete Stage 5 data entry to provide step-specific input values."
        )
        logger.warning(f"Test data validation failed: {error_msg}")
        pytest.skip(error_msg)
    
    # Check 5: Validate each manual input field
    for key in manual_input_keys:
        value = test_data.get(key)
        
        # Check for empty values
        if not value or str(value).strip() == "":
            error_msg = (
                f"Manual input data for '{key}' is empty. "
                f"Please enter a real value for this field in Stage 5."
            )
            logger.warning(f"Test data validation failed: {error_msg}")
            pytest.skip(error_msg)
        
        # Check for exact placeholder matches
        value_str = str(value).strip()
        if value_str.lower() in [p.lower() for p in PLACEHOLDER_VALUES]:
            error_msg = (
                f"Manual input data for '{key}' contains placeholder value '{value}'. "
                f"Please enter a real value for this field in Stage 5."
            )
            logger.warning(f"Test data validation failed: {error_msg}")
            pytest.skip(error_msg)
        
        # Check for placeholder patterns
        value_lower = value_str.lower()
        for pattern in PLACEHOLDER_PATTERNS:
            if pattern in value_lower:
                error_msg = (
                    f"Manual input data for '{key}' contains placeholder pattern '{pattern}' in value '{value}'. "
                    f"Please enter a real value for this field in Stage 5."
                )
                logger.warning(f"Test data validation failed: {error_msg}")
                pytest.skip(error_msg)
    
    # Check 6: Validate that we have meaningful data length
    for key in manual_input_keys:
        value = str(test_data.get(key, "")).strip()
        if len(value) < 2:  # Very short values are likely placeholders
            error_msg = (
                f"Manual input data for '{key}' is too short ('{value}'). "
                f"Please enter a meaningful value for this field in Stage 5."
            )
            logger.warning(f"Test data validation failed: {error_msg}")
            pytest.skip(error_msg)
    
    # All validations passed
    logger.info(f"Test data validation passed. Found {len(manual_input_keys)} valid manual input fields.")
    return True


def validate_specific_field(test_data, field_name, min_length=1):
    """
    Validate a specific field in test data.
    
    Args:
        test_data (dict): Test data dictionary
        field_name (str): Name of the field to validate
        min_length (int): Minimum required length for the field value
        
    Raises:
        pytest.skip: If field validation fails
        
    Returns:
        str: The validated field value
    """
    if field_name not in test_data:
        error_msg = (
            f"Required field '{field_name}' not found in test data. "
            f"Please ensure this field is provided in Stage 5."
        )
        logger.warning(f"Field validation failed: {error_msg}")
        pytest.skip(error_msg)
    
    value = str(test_data[field_name]).strip()
    
    if len(value) < min_length:
        error_msg = (
            f"Field '{field_name}' value is too short ('{value}'). "
            f"Please provide a meaningful value with at least {min_length} characters."
        )
        logger.warning(f"Field validation failed: {error_msg}")
        pytest.skip(error_msg)
    
    # Check for placeholder patterns
    value_lower = value.lower()
    for pattern in PLACEHOLDER_PATTERNS:
        if pattern in value_lower:
            error_msg = (
                f"Field '{field_name}' contains placeholder pattern '{pattern}' in value '{value}'. "
                f"Please enter a real value for this field in Stage 5."
            )
            logger.warning(f"Field validation failed: {error_msg}")
            pytest.skip(error_msg)
    
    logger.info(f"Field '{field_name}' validation passed with value: '{value}'")
    return value


def get_validation_summary(test_data):
    """
    Get a summary of test data validation status without skipping the test.
    
    Args:
        test_data (dict): Test data dictionary
        
    Returns:
        dict: Validation summary with status and details
    """
    summary = {
        "is_valid": True,
        "issues": [],
        "manual_input_fields": [],
        "has_runtime_file": os.path.exists("test_data_runtime.json"),
        "is_fallback_data": test_data.get("_is_fallback_data", False)
    }
    
    # Check for manual input fields
    manual_input_keys = [key for key in test_data.keys() if key.startswith('manual_input_for_step_')]
    summary["manual_input_fields"] = manual_input_keys
    
    if not manual_input_keys:
        summary["is_valid"] = False
        summary["issues"].append("No manual input fields found")
    
    # Check each manual input field
    for key in manual_input_keys:
        value = test_data.get(key)
        if not value or str(value).strip() == "":
            summary["is_valid"] = False
            summary["issues"].append(f"Empty value for {key}")
        else:
            value_str = str(value).strip().lower()
            for pattern in PLACEHOLDER_PATTERNS:
                if pattern in value_str:
                    summary["is_valid"] = False
                    summary["issues"].append(f"Placeholder pattern '{pattern}' found in {key}")
                    break
    
    return summary


if __name__ == "__main__":
    # Test the validator with sample data
    print("Testing test data validator...")
    
    # Test with placeholder data
    placeholder_data = {
        "manual_input_for_step_1": "test_value_1",
        "manual_input_for_step_2": "placeholder_user",
        "_is_fallback_data": True
    }
    
    summary = get_validation_summary(placeholder_data)
    print(f"Placeholder data validation: {summary}")
    
    # Test with real data
    real_data = {
        "manual_input_for_step_1": "<EMAIL>",
        "manual_input_for_step_2": "mypassword123",
        "manual_input_for_step_3": "John Doe"
    }
    
    summary = get_validation_summary(real_data)
    print(f"Real data validation: {summary}")
