#!/usr/bin/env python3
"""
Test script to verify the test data validation system works correctly.

This script tests various scenarios to ensure that:
1. Tests skip when placeholder data is detected
2. Tests skip when test_data_runtime.json is missing
3. Tests proceed when real data is available
4. Validation messages are clear and helpful
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_data_validator import validate_test_data, get_validation_summary


def test_validation_with_placeholder_data():
    """Test that validation fails with placeholder data."""
    print("\n=== Testing Validation with Placeholder Data ===")
    
    placeholder_data = {
        "manual_input_for_step_1": "test_value_1",
        "manual_input_for_step_2": "placeholder_user", 
        "manual_input_for_step_3": "dummy_password",
        "_is_fallback_data": True
    }
    
    summary = get_validation_summary(placeholder_data)
    print(f"Validation summary: {summary}")
    
    if not summary["is_valid"]:
        print("✅ PASS: Validation correctly identified placeholder data")
        print(f"   Issues found: {summary['issues']}")
    else:
        print("❌ FAIL: Validation should have failed with placeholder data")
    
    return not summary["is_valid"]


def test_validation_with_real_data():
    """Test that validation passes with real data."""
    print("\n=== Testing Validation with Real Data ===")
    
    real_data = {
        "manual_input_for_step_1": "<EMAIL>",
        "manual_input_for_step_2": "mypassword123",
        "manual_input_for_step_3": "John Doe",
        "manual_input_for_step_4": "555-123-4567"
    }
    
    summary = get_validation_summary(real_data)
    print(f"Validation summary: {summary}")
    
    if summary["is_valid"]:
        print("✅ PASS: Validation correctly accepted real data")
        print(f"   Manual input fields: {summary['manual_input_fields']}")
    else:
        print("❌ FAIL: Validation should have passed with real data")
        print(f"   Issues found: {summary['issues']}")
    
    return summary["is_valid"]


def test_validation_with_empty_data():
    """Test that validation fails with empty data."""
    print("\n=== Testing Validation with Empty Data ===")
    
    empty_data = {
        "manual_input_for_step_1": "",
        "manual_input_for_step_2": "   ",  # Whitespace only
        "manual_input_for_step_3": None
    }
    
    summary = get_validation_summary(empty_data)
    print(f"Validation summary: {summary}")
    
    if not summary["is_valid"]:
        print("✅ PASS: Validation correctly identified empty data")
        print(f"   Issues found: {summary['issues']}")
    else:
        print("❌ FAIL: Validation should have failed with empty data")
    
    return not summary["is_valid"]


def test_validation_with_no_manual_fields():
    """Test that validation fails when no manual input fields are present."""
    print("\n=== Testing Validation with No Manual Fields ===")
    
    no_manual_data = {
        "username": "testuser",
        "password": "testpass",
        "email": "<EMAIL>"
    }
    
    summary = get_validation_summary(no_manual_data)
    print(f"Validation summary: {summary}")
    
    if not summary["is_valid"]:
        print("✅ PASS: Validation correctly identified missing manual input fields")
        print(f"   Issues found: {summary['issues']}")
    else:
        print("❌ FAIL: Validation should have failed with no manual input fields")
    
    return not summary["is_valid"]


def test_runtime_file_detection():
    """Test that validation detects presence/absence of test_data_runtime.json."""
    print("\n=== Testing Runtime File Detection ===")
    
    # Test without runtime file
    if os.path.exists("test_data_runtime.json"):
        # Temporarily rename the file
        shutil.move("test_data_runtime.json", "test_data_runtime.json.backup")
        file_was_present = True
    else:
        file_was_present = False
    
    test_data = {"manual_input_for_step_1": "realvalue"}
    summary = get_validation_summary(test_data)
    
    if not summary["has_runtime_file"]:
        print("✅ PASS: Validation correctly detected missing runtime file")
    else:
        print("❌ FAIL: Validation should have detected missing runtime file")
    
    # Restore the file if it was present
    if file_was_present:
        shutil.move("test_data_runtime.json.backup", "test_data_runtime.json")
    
    # Test with runtime file present
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump({"manual_input_for_step_1": "realvalue"}, f)
        temp_file = f.name
    
    # Move temp file to expected location
    if os.path.exists("test_data_runtime.json"):
        shutil.move("test_data_runtime.json", "test_data_runtime.json.backup2")
    shutil.move(temp_file, "test_data_runtime.json")
    
    summary = get_validation_summary(test_data)
    
    if summary["has_runtime_file"]:
        print("✅ PASS: Validation correctly detected present runtime file")
        result = True
    else:
        print("❌ FAIL: Validation should have detected present runtime file")
        result = False
    
    # Cleanup
    os.remove("test_data_runtime.json")
    if os.path.exists("test_data_runtime.json.backup2"):
        shutil.move("test_data_runtime.json.backup2", "test_data_runtime.json")
    
    return result


def test_fallback_data_detection():
    """Test that validation detects fallback data indicators."""
    print("\n=== Testing Fallback Data Detection ===")
    
    fallback_data = {
        "manual_input_for_step_1": "realvalue",
        "manual_input_for_step_2": "anotherrealvalue",
        "_is_fallback_data": True,
        "_warning": "This is fallback test data"
    }
    
    summary = get_validation_summary(fallback_data)
    print(f"Validation summary: {summary}")
    
    if summary["is_fallback_data"]:
        print("✅ PASS: Validation correctly detected fallback data indicator")
    else:
        print("❌ FAIL: Validation should have detected fallback data indicator")
    
    return summary["is_fallback_data"]


def run_all_tests():
    """Run all validation tests and report results."""
    print("🧪 Testing GretahAI ScriptWeaver Test Data Validation System")
    print("=" * 60)
    
    tests = [
        ("Placeholder Data Detection", test_validation_with_placeholder_data),
        ("Real Data Acceptance", test_validation_with_real_data),
        ("Empty Data Detection", test_validation_with_empty_data),
        ("Missing Manual Fields Detection", test_validation_with_no_manual_fields),
        ("Runtime File Detection", test_runtime_file_detection),
        ("Fallback Data Detection", test_fallback_data_detection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
        except Exception as e:
            print(f"❌ ERROR in {test_name}: {e}")
            results.append((test_name, False, str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result, error in results:
        if result:
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED")
            if error:
                print(f"   Error: {error}")
            failed += 1
    
    print(f"\nTotal: {len(results)} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! The validation system is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. The validation system needs attention.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
