#!/usr/bin/env python3
"""
Demo test script to show how the validation system works in practice.

This script simulates a generated test script with validation logic included.
"""

import os
os.environ["WDM_LOG_LEVEL"] = "0"
import time
import random
import pytest
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By


def validate_test_data(test_data):
    """Validate that test data contains real user input, not placeholders."""
    import pytest
    import os
    
    # Check for missing test_data_runtime.json (indicates Stage 5 not completed)
    if not os.path.exists("test_data_runtime.json"):
        pytest.skip("Manual input data not available - please complete Stage 5 data entry first")
    
    # Define placeholder patterns that indicate invalid test data
    placeholder_patterns = [
        "test_value_", "placeholder_", "default_", "sample_", 
        "example_", "dummy_", "mock_", "fake_"
    ]
    
    # Check each manual input field for placeholder values
    for key, value in test_data.items():
        if key.startswith('manual_input_for_step_'):
            if not value or str(value).strip() == "":
                pytest.skip(f"Manual input data for {key} is empty - please complete Stage 5 data entry")
            
            # Check if value matches placeholder patterns
            value_str = str(value).lower()
            for pattern in placeholder_patterns:
                if pattern in value_str:
                    pytest.skip(f"Manual input data contains placeholder value '{value}' - please enter real data in Stage 5")
    
    return True


def test_step1_navigate(browser, test_data):
    """Test step 1: Navigate to login page."""
    try:
        # Call validation at start of test function
        validate_test_data(test_data)
        
        # Navigate to the login page
        browser.get("https://the-internet.herokuapp.com/login")
        time.sleep(random.uniform(0.5, 1.5))
        
        # Assert that we're on the correct page
        assert "login" in browser.current_url.lower(), "Failed to navigate to login page"
        
    except Exception as e:
        os.makedirs('screenshots', exist_ok=True)
        browser.save_screenshot(f"screenshots/navigate_{int(time.time())}.png")
        raise


def test_step2_enter_username(browser, test_data):
    """Test step 2: Enter username."""
    try:
        # Call validation at start of test function
        validate_test_data(test_data)
        
        # Access test data values
        manual_input_for_step_2 = test_data["manual_input_for_step_2"]
        
        # Find the username input field
        username_field = WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        
        # Enter the username
        username_field.send_keys(manual_input_for_step_2)
        time.sleep(random.uniform(0.5, 1.5))
        
        # Assert that the username was entered
        assert username_field.get_attribute("value") == manual_input_for_step_2, "Username was not entered correctly"
        
    except Exception as e:
        os.makedirs('screenshots', exist_ok=True)
        browser.save_screenshot(f"screenshots/username_{int(time.time())}.png")
        raise


def test_step3_enter_password(browser, test_data):
    """Test step 3: Enter password."""
    try:
        # Call validation at start of test function
        validate_test_data(test_data)
        
        # Access test data values
        manual_input_for_step_3 = test_data["manual_input_for_step_3"]
        
        # Find the password input field
        password_field = WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.ID, "password"))
        )
        
        # Enter the password
        password_field.send_keys(manual_input_for_step_3)
        time.sleep(random.uniform(0.5, 1.5))
        
        # Assert that the password was entered (check that field is not empty)
        assert password_field.get_attribute("value") != "", "Password was not entered"
        
    except Exception as e:
        os.makedirs('screenshots', exist_ok=True)
        browser.save_screenshot(f"screenshots/password_{int(time.time())}.png")
        raise


if __name__ == "__main__":
    print("This is a demo test script showing validation integration.")
    print("Run with pytest to see validation in action:")
    print("  pytest test_validation_demo.py -v -s")
